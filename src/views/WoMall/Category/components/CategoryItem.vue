<template>
  <div
    class="category-item"
    :style="{ width: widthStyle }"
    @click="handleClick"
  >
    <div class="category-item__icon">
      <img
        :src="imageUrl || defaultIcon"
        :alt="name"
        loading="lazy"
        decoding="async"
        width="60"
        height="60"
      />
    </div>
    <span class="category-item__name">{{ name }}</span>
  </div>
</template>

<script setup>
const {
  id,
  name,
  imageUrl,
  widthStyle,
  defaultIcon = 'https://img01.yzcdn.cn/vant/cat.jpeg'
} = defineProps({
  id: {
    type: [String, Number],
    required: true
  },
  name: {
    type: String,
    required: true
  },
  imageUrl: {
    type: String,
    default: ''
  },
  widthStyle: {
    type: String,
    default: '33.333%'
  },
  defaultIcon: {
    type: String,
    default: 'https://img01.yzcdn.cn/vant/cat.jpeg'
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click', { id, name, imageUrl })
}
</script>

<style scoped lang="less">
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 5px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: transform 0.2s ease;
  will-change: transform;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }

  &__icon {
    width: 60px;
    height: 60px;
    margin-bottom: 5px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    will-change: transform;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: transform 0.2s ease;
    }
  }

  &__name {
    font-size: @font-size-12;
    color: @text-color-secondary;
    text-align: center;
    width: 100%;
    line-height: 1.2;
    word-break: break-word;
    .multi-ellipsis(2);
  }
}
</style>

<template>
  <aside class="category-sidebar">
    <!-- 骨架屏加载状态 -->
    <CategorySkeleton
      v-if="isLoading"
      type="sidebar"
      :count="8"
    />

    <!-- 侧边栏内容 -->
    <van-sidebar
      v-else
      v-model="activeIndex"
      @change="handleChange"
      ref="sidebarRef"
    >
      <van-sidebar-item
        v-for="category in categories"
        :key="category.id"
        :title="category.name"
        ref="sidebarItemRefs"
      />
    </van-sidebar>
  </aside>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import CategorySkeleton from './CategorySkeleton.vue'

const {
  categories = [],
  isLoading = false,
  activeIndex: propActiveIndex = 0
} = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  activeIndex: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['change'])

const activeIndex = ref(propActiveIndex)
const sidebarRef = ref(null)
const sidebarItemRefs = ref([])

const handleChange = (index) => {
  activeIndex.value = index
  emit('change', index, categories[index])
}

const scrollToCategory = (index) => {
  nextTick(() => {
    if (!sidebarRef.value || !sidebarItemRefs.value[index]) return

    const sidebarEl = sidebarRef.value.$el
    const selectedItem = sidebarItemRefs.value[index].$el

    if (sidebarEl && selectedItem) {
      const itemTop = selectedItem.offsetTop
      const sidebarHeight = sidebarEl.clientHeight
      const itemHeight = selectedItem.clientHeight

      sidebarEl.scrollTo({
        top: itemTop - (sidebarHeight / 2) + (itemHeight / 2),
        behavior: 'smooth'
      })
    }
  })
}

const setActiveCategory = (index) => {
  if (index >= 0 && index < categories.length) {
    activeIndex.value = index
    scrollToCategory(index)
  }
}

defineExpose({
  setActiveCategory,
  scrollToCategory
})
</script>

<style scoped lang="less">
.category-sidebar {
  width: 110px;
  flex-shrink: 0;

  :deep(.van-sidebar) {
    width: 110px;
    height: 100%;
    overflow-y: auto;
    background-color: @bg-color-gray;
    text-align: center;
    .no-scrollbar();
  }

  :deep(.van-sidebar-item) {
    padding: 12px 6px;
    font-size: @font-size-13;
    color: @text-color-primary;
    transition: all 0.2s ease;

    &--select {
      color: @theme-color;
      font-weight: @font-weight-500;
      border-color: @theme-color;

      &::before {
        background-color: @theme-color;
      }
    }
  }
}
</style>

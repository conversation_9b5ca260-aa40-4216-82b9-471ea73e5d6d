<template>
  <div class="category-skeleton">
    <!-- 侧边栏骨架屏 -->
    <div v-if="type === 'sidebar'" class="category-skeleton__sidebar">
      <div 
        v-for="i in count" 
        :key="i" 
        class="category-skeleton__sidebar-item"
      >
        <div class="category-skeleton__sidebar-text"></div>
      </div>
    </div>

    <!-- 主内容骨架屏 -->
    <div v-else-if="type === 'content'" class="category-skeleton__content">
      <div 
        v-for="i in count" 
        :key="i" 
        class="category-skeleton__section"
      >
        <div class="category-skeleton__section-title"></div>
        <div class="category-skeleton__grid">
          <div 
            v-for="j in itemsPerSection" 
            :key="j" 
            class="category-skeleton__item"
          >
            <div class="category-skeleton__item-icon"></div>
            <div class="category-skeleton__item-name"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 网格骨架屏 -->
    <div v-else-if="type === 'grid'" class="category-skeleton__grid">
      <div 
        v-for="i in count" 
        :key="i" 
        class="category-skeleton__item"
      >
        <div class="category-skeleton__item-icon"></div>
        <div class="category-skeleton__item-name"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props 解构
const {
  type = 'content',
  count = 3,
  itemsPerSection = 3
} = defineProps({
  type: {
    type: String,
    default: 'content',
    validator: (value) => ['sidebar', 'content', 'grid'].includes(value)
  },
  count: {
    type: Number,
    default: 3
  },
  itemsPerSection: {
    type: Number,
    default: 3
  }
})
</script>

<style scoped lang="less">
.category-skeleton {
  &__sidebar {
    width: 110px;
    height: 100%;
    overflow-y: auto;
    background-color: @bg-color-gray;
    .no-scrollbar();
  }

  &__sidebar-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 6px;
  }

  &__sidebar-text {
    width: 60px;
    height: 14px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }

  &__content {
    padding: @padding-page * 2;
  }

  &__section {
    margin-bottom: @padding-page * 2;
    padding: @padding-page * 2;
    background-color: @bg-color-white;
    border-radius: @radius-8;
    box-shadow: 0 2px 8px rgba(227, 227, 242, 0.5);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__section-title {
    width: 80px;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  &__grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding: 5px;
    margin-bottom: 15px;
    flex: 0 0 33.333%;
  }

  &__item-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
    margin-bottom: 8px;
  }

  &__item-name {
    width: 50px;
    height: 12px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 6px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>

<template>
  <MainLayout scroll="auto">
    <div class="category-page">
      <!-- 搜索头部 -->
      <SearchHeader
        v-model="searchKeyword"
        placeholder="搜索商品"
        :redirect-to-search="true"
        redirect-url="/search"
        @search="handleSearch"
      />

      <!-- 分类主体布局 -->
      <div class="category-page__layout">
        <!-- 侧边栏 -->
        <CategorySidebar
          :categories="firstCategories"
          :is-loading="isFirstCategoryLoading"
          :active-index="activeFirstCategory"
          @change="handleFirstCategoryChange"
          ref="sidebarRef"
        />

        <!-- 主内容区 -->
        <main class="category-page__main" ref="categoryMainRef">
          <!-- 一级分类加载骨架屏 -->
          <CategorySkeleton
            v-if="isFirstCategoryLoading"
            type="content"
            :count="3"
            :items-per-section="3"
          />

          <!-- 二级分类加载骨架屏 -->
          <CategorySkeleton
            v-else-if="isSecondCategoryLoading"
            type="content"
            :count="3"
            :items-per-section="3"
          />

          <!-- 分类内容 -->
          <div v-else class="category-page__content">
            <section
              v-for="(group, index) in thirdCategoriesGroups"
              :key="group.id || index"
              class="category-page__section"
              :data-category-id="group.id"
            >
              <h3 class="category-page__section-title">{{ group.title }}</h3>

              <!-- 分类项目列表 -->
              <div v-if="group.items.length > 0" class="category-page__items">
                <CategoryItem
                  v-for="item in group.items"
                  :key="item.id"
                  :id="item.id"
                  :name="item.name"
                  :image-url="item.img"
                  :width-style="itemWidthStyle"
                  :default-icon="defaultIcon"
                  @click="handleCategoryClick"
                />
              </div>

              <!-- 加载状态骨架屏 -->
              <CategorySkeleton
                v-else-if="group.isLoading"
                type="grid"
                :count="3"
              />

              <!-- 空状态 -->
              <div v-else-if="group.isEmpty" class="category-page__empty">
                <span class="category-page__empty-text">暂无商品分类</span>
              </div>

              <!-- 占位区域 -->
              <div v-else class="category-page__placeholder">
                <div class="category-page__load-trigger" :data-second-id="group.id"></div>
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useElementSize } from '@vueuse/core'
import { debounce } from 'lodash-es'
import { closeToast, showLoadingToast, showToast } from 'vant'
import SearchHeader from '@components/Common/SearchHeader.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import CategorySkeleton from './components/CategorySkeleton.vue'
import CategoryItem from './components/CategoryItem.vue'
import CategorySidebar from './components/CategorySidebar.vue'
import { getClassification } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'
import defaultIcon from './assets/placeholder-loading.png'
const router = useRouter()
const route = useRoute()

// 基础数据
const searchKeyword = ref('')

// 分类数据
const firstCategories = ref([])
const secondCategories = ref([])
const thirdCategories = ref(new Map())
const activeFirstCategory = ref(0)

// 加载状态
const isFirstCategoryLoading = ref(true)
const isSecondCategoryLoading = ref(false)
const loadingThirdCategories = ref(new Set())
const loadedSecondCategoryIds = ref(new Set())

// 组件引用
const sidebarRef = ref(null)
const categoryMainRef = ref(null)

// 响应式布局
const { width: containerWidth } = useElementSize(categoryMainRef)
const itemsPerRow = computed(() => Math.max(Math.floor(containerWidth.value / 100), 3))
const itemWidthStyle = computed(() => `${100 / itemsPerRow.value}%`)

// 滚动监听
// const { y: scrollY } = useScroll(categoryMainRef)

// 常量
const INITIAL_LOAD_COUNT = 3
const LOAD_THRESHOLD = 200

// API缓存
const apiCache = new Map()
const getCacheKey = (params) => JSON.stringify(params)

// 计算三级分类分组
const thirdCategoriesGroups = computed(() => {
  const groups = []
  secondCategories.value.forEach(secondCategory => {
    const items = thirdCategories.value.get(secondCategory.id) || []
    const isLoading = loadingThirdCategories.value.has(secondCategory.id)
    const isLoaded = loadedSecondCategoryIds.value.has(secondCategory.id)

    groups.push({
      id: secondCategory.id,
      title: secondCategory.name,
      items: items,
      isLoading: isLoading,
      isLoaded: isLoaded,
      isEmpty: isLoaded && items.length === 0
    })
  })
  return groups
})

// 事件处理
const handleSearch = () => {
  // 搜索功能由 SearchHeader 组件处理
}

const handleFirstCategoryChange = (index, category) => {
  if (!category) return

  if (categoryMainRef.value) {
    categoryMainRef.value.scrollTop = 0
  }

  router.replace({ path: `/category/${category.id}` }).catch(() => {})
  fetchSecondAndThirdCategories(category.id)
}

const handleCategoryClick = (categoryData) => {
  router.push({ path: `/goodslist/${categoryData.id}` })
}

// API调用
const fetchCategories = async (id = '') => {
  const cacheKey = getCacheKey({ category_pid: id, page_no: 1, page_size: 500 })

  try {
    if (id === '') {
      isFirstCategoryLoading.value = true
    } else if (!isFirstCategoryLoading.value) {
      isSecondCategoryLoading.value = true
      showLoadingToast()
    }

    if (apiCache.has(cacheKey)) {
      const cachedData = apiCache.get(cacheKey)
      await processCategories(cachedData, id)
      return
    }

    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: id,
      page_no: 1,
      page_size: 500
    })

    if (err) {
      showToast('获取分类数据失败')
      return
    }

    if (data && Array.isArray(data)) {
      apiCache.set(cacheKey, data)
      setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000)
      await processCategories(data, id)
    }
  } catch (error) {
    console.error('获取分类数据出错:', error)
    showToast('获取分类数据失败')
  } finally {
    if (id === '') {
      isFirstCategoryLoading.value = false
    } else {
      isSecondCategoryLoading.value = false
    }
    closeToast()
  }
}

const processCategories = async (data, id) => {
  if (id === '') {
    firstCategories.value = data.filter(item => item.depth === 1)

    const routeCategoryId = route.params.id
    if (routeCategoryId && firstCategories.value.length > 0) {
      const found = findAndSetActiveCategoryById(routeCategoryId)
      if (!found && firstCategories.value.length > 0) {
        activeFirstCategory.value = 0
        await fetchSecondAndThirdCategories(firstCategories.value[0].id)
      }
    } else if (firstCategories.value.length > 0) {
      activeFirstCategory.value = 0
      await fetchSecondAndThirdCategories(firstCategories.value[0].id)
    }
  } else {
    const secondLevel = data.filter(item => item.depth === 2)
    secondCategories.value = secondLevel

    thirdCategories.value.clear()
    loadedSecondCategoryIds.value.clear()
    loadingThirdCategories.value.clear()

    if (secondLevel.length > 0) {
      await loadInitialThirdCategories(secondLevel)
      nextTick(() => setupScrollListener())
    }
  }
}

const fetchSecondAndThirdCategories = async (firstCategoryId) => {
  if (!firstCategoryId) return
  await fetchCategories(firstCategoryId)
}

const fetchThirdCategories = async (secondCategoryId) => {
  if (!secondCategoryId) return []

  const cacheKey = getCacheKey({ category_pid: secondCategoryId, page_no: 1, page_size: 500 })

  if (apiCache.has(cacheKey)) {
    const cachedData = apiCache.get(cacheKey)
    return cachedData.filter(item => item.depth === 3)
  }

  try {
    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: secondCategoryId,
      page_no: 1,
      page_size: 500
    })

    if (err || !data) return []

    apiCache.set(cacheKey, data)
    setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000)

    return data.filter(item => item.depth === 3)
  } catch (error) {
    console.error('获取三级分类出错:', error)
    return []
  }
}

const loadInitialThirdCategories = async (secondLevel) => {
  const initialCategories = secondLevel.slice(0, INITIAL_LOAD_COUNT)

  const loadPromises = initialCategories.map(async (secondCategory) => {
    loadingThirdCategories.value.add(secondCategory.id)

    try {
      const thirdItems = await fetchThirdCategories(secondCategory.id)
      thirdCategories.value.set(secondCategory.id, thirdItems)
      loadedSecondCategoryIds.value.add(secondCategory.id)
    } catch (error) {
      console.error(`加载二级分类 ${secondCategory.id} 的三级分类失败:`, error)
      thirdCategories.value.set(secondCategory.id, [])
      loadedSecondCategoryIds.value.add(secondCategory.id)
    } finally {
      loadingThirdCategories.value.delete(secondCategory.id)
    }
  })

  await Promise.all(loadPromises)
}

const loadThirdCategoryLazy = async (secondCategoryId) => {
  if (loadedSecondCategoryIds.value.has(secondCategoryId) ||
      loadingThirdCategories.value.has(secondCategoryId)) {
    return
  }

  loadingThirdCategories.value.add(secondCategoryId)

  try {
    const thirdItems = await fetchThirdCategories(secondCategoryId)
    thirdCategories.value.set(secondCategoryId, thirdItems)
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } catch (error) {
    console.error(`懒加载二级分类 ${secondCategoryId} 的三级分类失败:`, error)
    thirdCategories.value.set(secondCategoryId, [])
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } finally {
    loadingThirdCategories.value.delete(secondCategoryId)
  }
}

const findAndSetActiveCategoryById = (categoryId) => {
  if (!categoryId || !firstCategories.value.length) return false

  const index = firstCategories.value.findIndex(category => category.id === categoryId)
  if (index !== -1) {
    activeFirstCategory.value = index
    return true
  }
  return false
}

// 懒加载逻辑
let scrollListener = null

const setupScrollListener = () => {
  if (!categoryMainRef.value) return

  if (scrollListener) {
    categoryMainRef.value.removeEventListener('scroll', scrollListener)
  }

  const debouncedCheck = debounce(() => {
    checkAndLoadVisibleCategories()
  }, 100)

  scrollListener = debouncedCheck
  categoryMainRef.value.addEventListener('scroll', scrollListener, { passive: true })

  nextTick(() => checkAndLoadVisibleCategories())
}

const checkAndLoadVisibleCategories = () => {
  if (!categoryMainRef.value) return

  const container = categoryMainRef.value
  const containerRect = container.getBoundingClientRect()
  const containerTop = containerRect.top
  const containerBottom = containerRect.bottom

  const sections = container.querySelectorAll('.category-page__section')

  sections.forEach(section => {
    const sectionRect = section.getBoundingClientRect()
    const secondCategoryId = section.dataset.categoryId

    if (!secondCategoryId || loadedSecondCategoryIds.value.has(secondCategoryId)) {
      return
    }

    const isVisible = sectionRect.top < containerBottom + LOAD_THRESHOLD &&
                     sectionRect.bottom > containerTop - LOAD_THRESHOLD

    if (isVisible) {
      loadThirdCategoryLazy(secondCategoryId)
    }
  })
}

// 生命周期
onMounted(async () => {
  await fetchCategories()
})

onBeforeUnmount(() => {
  if (scrollListener && categoryMainRef.value) {
    categoryMainRef.value.removeEventListener('scroll', scrollListener)
  }
})
</script>

<style scoped lang="less">
.category-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: @bg-color-gray;

  &__layout {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  &__main {
    flex: 1;
    overflow-y: auto;
    background-color: @bg-color-white;
    contain: layout style paint;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    .no-scrollbar();
  }

  &__content {
    padding: @padding-page * 2;
  }

  &__section {
    margin-bottom: @padding-page * 2;
    padding: @padding-page * 2;
    background-color: @bg-color-white;
    border-radius: @radius-8;
    box-shadow: 0 2px 8px rgba(227, 227, 242, 0.5);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__section-title {
    margin: 0 0 10px 0;
    padding: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-500;
    color: @text-color-primary;
    line-height: 1.4;
  }

  &__items {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }

  &__empty-text {
    font-size: @font-size-13;
    color: @text-color-tertiary;
  }

  &__placeholder {
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__load-trigger {
    width: 100%;
    height: 20px;
    background: transparent;
  }
}


</style>

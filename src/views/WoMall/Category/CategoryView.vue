<template>
  <MainLayout scroll="auto">
    <div class="category-page">
      <!-- 搜索头部 -->
      <SearchHeader
        v-model="searchKeyword"
        placeholder="搜索商品"
        :redirect-to-search="true"
        redirect-url="/search"
        @search="handleSearch"
      />

      <!-- 分类主体布局 -->
      <div class="category-page__layout">
        <!-- 侧边栏 -->
        <CategorySidebar
          :categories="firstCategories"
          :is-loading="isFirstCategoryLoading"
          :active-index="activeFirstCategory"
          @change="handleFirstCategoryChange"
          ref="sidebarRef"
        />

        <!-- 主内容区 -->
        <main class="category-page__main" ref="categoryMainRef">
          <!-- 一级分类加载骨架屏 -->
          <CategorySkeleton
            v-if="isFirstCategoryLoading"
            type="content"
            :count="3"
            :items-per-section="3"
          />

          <!-- 二级分类加载骨架屏 -->
          <CategorySkeleton
            v-else-if="isSecondCategoryLoading"
            type="content"
            :count="3"
            :items-per-section="3"
          />

          <!-- 分类内容 -->
          <div v-else class="category-page__content">
            <section
              v-for="(group, index) in thirdCategoriesGroups"
              :key="group.id || index"
              class="category-page__section"
              :data-category-id="group.id"
            >
              <h3 class="category-page__section-title">{{ group.title }}</h3>

              <!-- 分类项目列表 -->
              <div v-if="group.items.length > 0" class="category-page__items">
                <CategoryItem
                  v-for="item in group.items"
                  :key="item.id"
                  :id="item.id"
                  :name="item.name"
                  :image-url="item.img"
                  :width-style="itemWidthStyle"
                  :default-icon="defaultIcon"
                  @click="handleCategoryClick"
                />
              </div>

              <!-- 加载状态骨架屏 -->
              <CategorySkeleton
                v-else-if="group.isLoading"
                type="grid"
                :count="3"
              />

              <!-- 空状态 -->
              <div v-else-if="group.isEmpty" class="category-page__empty">
                <span class="category-page__empty-text">暂无商品分类</span>
              </div>

              <!-- 占位区域 -->
              <div v-else class="category-page__placeholder">
                <div class="category-page__load-trigger" :data-second-id="group.id"></div>
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SearchHeader from '@components/Common/SearchHeader.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import CategorySkeleton from '@views/WoMall/Category/components/CategorySkeleton.vue'
import CategoryItem from '@views/WoMall/Category/components/CategoryItem.vue'
import CategorySidebar from '@views/WoMall/Category/components/CategorySidebar.vue'

// 组合式函数
import { useCategory } from '@views/WoMall/Category/composables/useCategory.js'
import { useLazyLoad, useResponsiveLayout } from '@views/WoMall/Category/composables/useLazyLoad.js'

// ==================== 路由和基础数据 ====================
const router = useRouter()
const route = useRoute()

// Props 解构（如果有的话）
// const props = defineProps({
//   // 预留 props 定义
// })

// 搜索关键词
const searchKeyword = ref('')

// 默认图标
const defaultIcon = 'https://img01.yzcdn.cn/vant/cat.jpeg'

// ==================== 组合式函数使用 ====================
// 分类数据管理
const {
  firstCategories,
  secondCategories,
  activeFirstCategory,
  isFirstCategoryLoading,
  isSecondCategoryLoading,
  loadedSecondCategoryIds,
  thirdCategoriesGroups,
  fetchCategories,
  loadThirdCategoryLazy,
  findAndSetActiveCategoryById,
  fetchSecondAndThirdCategories,
  LOAD_THRESHOLD
} = useCategory()

// 懒加载功能
const {
  containerRef: categoryMainRef,
  setupScrollListener,
  triggerCheck
} = useLazyLoad({
  threshold: LOAD_THRESHOLD,
  debounceDelay: 100
})

// 响应式布局
const {
  containerRef: layoutContainerRef,
  itemsPerRow,
  setupResponsiveListener
} = useResponsiveLayout({
  minItemWidth: 100,
  minItemsPerRow: 3,
  debounceDelay: 150
})

// ==================== 计算属性 ====================
const itemWidthStyle = computed(() => `${100 / itemsPerRow.value}%`)

// ==================== 组件引用 ====================
const sidebarRef = ref(null)

// ==================== 事件处理函数 ====================
/**
 * 搜索处理函数
 */
const handleSearch = () => {
  // 搜索功能由 SearchHeader 组件的 redirectToSearch 处理
}

/**
 * 一级分类切换处理函数
 */
const handleFirstCategoryChange = (index, category) => {
  if (!category) return

  // 立即重置滚动位置到顶部
  if (categoryMainRef.value) {
    categoryMainRef.value.scrollTop = 0
  }

  // 更新路由参数，但不重新加载页面
  router.replace({
    path: `/category/${category.id}`
  }).catch(() => {})

  // 获取对应的二级和三级分类
  fetchSecondAndThirdCategories(category.id)
}

/**
 * 分类项点击处理函数
 */
const handleCategoryClick = (categoryData) => {
  const { id } = categoryData

  // 跳转到对应分类的商品列表页
  router.push({
    path: `/goodslist/${id}`,
  })
}

/**
 * 懒加载检查回调
 */
const handleLazyLoadCheck = (categoryId) => {
  if (categoryId && !loadedSecondCategoryIds.value.has(categoryId)) {
    loadThirdCategoryLazy(categoryId)
  }
}

// ==================== 初始化和路由处理 ====================
/**
 * 初始化分类数据
 */
const initializeCategories = async () => {
  await fetchCategories()

  // 处理路由参数
  const routeCategoryId = route.params.id
  if (routeCategoryId && firstCategories.value.length > 0) {
    const found = findAndSetActiveCategoryById(routeCategoryId)
    if (!found && firstCategories.value.length > 0) {
      activeFirstCategory.value = 0
      await fetchSecondAndThirdCategories(firstCategories.value[0].id)
    }
  } else if (firstCategories.value.length > 0) {
    activeFirstCategory.value = 0
    await fetchSecondAndThirdCategories(firstCategories.value[0].id)
  }
}

// ==================== 生命周期 ====================
onMounted(async () => {
  // 初始化分类数据
  await initializeCategories()

  // 设置响应式布局监听
  nextTick(() => {
    // 将容器引用同步到响应式布局
    if (categoryMainRef.value) {
      layoutContainerRef.value = categoryMainRef.value
    }

    setupResponsiveListener()

    // 设置懒加载监听
    setupScrollListener(handleLazyLoadCheck)
  })
})

// 监听数据变化，重新设置懒加载
watch([secondCategories, thirdCategoriesGroups], () => {
  nextTick(() => {
    triggerCheck(handleLazyLoadCheck)
  })
}, { deep: true })
</script>

<style scoped lang="less">
// ==================== 页面主体样式 ====================
.category-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: @bg-color-gray;

  &__layout {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  &__main {
    flex: 1;
    overflow-y: auto;
    background-color: @bg-color-white;
    contain: layout style paint;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    .no-scrollbar();
  }

  &__content {
    padding: @padding-page * 2;
  }

  &__section {
    margin-bottom: @padding-page * 2;
    padding: @padding-page * 2;
    background-color: @bg-color-white;
    border-radius: @radius-8;
    box-shadow: 0 2px 8px rgba(227, 227, 242, 0.5);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__section-title {
    margin: 0 0 10px 0;
    padding: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-500;
    color: @text-color-primary;
    line-height: 1.4;
  }

  &__items {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }

  &__empty-text {
    font-size: @font-size-13;
    color: @text-color-tertiary;
  }

  &__placeholder {
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__load-trigger {
    width: 100%;
    height: 20px;
    background: transparent;
  }
}


</style>
